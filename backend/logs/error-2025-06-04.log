{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"address":"::","code":"EADDRINUSE","errno":-98,"level":"error","message":"❌ Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at Application.start (/home/<USER>/Suplementor/backend/src/index.ts:190:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"level":"error","message":"Error parsing Gemma analysis response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseAnalysisResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:265:27)\n    at GemmaService.analyzeSupplementData (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:74:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:108:24)","timestamp":"2025-06-04 00:02:12:212"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*magnaz i żelazo*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"magnaz i żelazo"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1.0 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"magnaz i żelazo","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"magnaz i żelazo","timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 609d873e-3e30-4b79-b681-5e6be82a00bb-2dabf81a-c7b1-4de3-b72d-c48fa94c258e: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
