{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:00:02:02"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"AG-UI WebSocket connection established: 02a669f3-9be5-4cb6-8380-b54ccc2b1022","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: 7f506975-c485-4bde-81e8-24f490386965","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: bb950cbf-7b54-40d2-8889-871a5767e56d","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: c347bfba-1100-4984-a472-40a2c5d3ad43","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: de876eda-d1bf-4510-9345-ceb50e24635c","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection closed: de876eda-d1bf-4510-9345-ceb50e24635c","timestamp":"2025-06-04 00:00:12:012"}
{"level":"info","message":"AG-UI WebSocket connection closed: 7f506975-c485-4bde-81e8-24f490386965","timestamp":"2025-06-04 00:00:12:012"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:48:048"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"AG-UI WebSocket connection established: 2aa295d9-6d40-43ef-9317-c72e9e265c06","timestamp":"2025-06-04 00:00:49:049"}
{"level":"info","message":"AG-UI WebSocket connection established: 31d93ac7-8214-4ada-879c-e8e8b9adc093","timestamp":"2025-06-04 00:00:51:051"}
{"level":"info","message":"AG-UI WebSocket connection closed: 02a669f3-9be5-4cb6-8380-b54ccc2b1022","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: c347bfba-1100-4984-a472-40a2c5d3ad43","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2aa295d9-6d40-43ef-9317-c72e9e265c06","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: bb950cbf-7b54-40d2-8889-871a5767e56d","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection established: 08f32c04-82e3-49bd-b44a-b40fa5d62b0a","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: 08f32c04-82e3-49bd-b44a-b40fa5d62b0a","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection established: bff6b134-3870-4f06-b475-13ebc45e88cd","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection established: 609d873e-3e30-4b79-b681-5e6be82a00bb","timestamp":"2025-06-04 00:01:35:135"}
{"level":"info","message":"Starting Gemma analysis for supplement: magnaz i żelazo","timestamp":"2025-06-04 00:02:12:212"}
{"level":"error","message":"Error parsing Gemma analysis response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseAnalysisResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:265:27)\n    at GemmaService.analyzeSupplementData (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:74:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:108:24)","timestamp":"2025-06-04 00:02:12:212"}
{"level":"info","message":"Gemma analysis completed for magnaz i żelazo","timestamp":"2025-06-04 00:02:12:212"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*magnaz i żelazo*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"magnaz i żelazo"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1.0 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"magnaz i żelazo","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"magnaz i żelazo","timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 609d873e-3e30-4b79-b681-5e6be82a00bb-2dabf81a-c7b1-4de3-b72d-c48fa94c258e: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
