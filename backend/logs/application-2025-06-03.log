{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:31:20:3120"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:31:20:3120"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:31:20:3120"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:31:20:3120"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:31:21:3121"}
{"error":"Weaviate client not initialized. Call connect() first.","level":"error","message":"Weaviate health check failed","stack":"Error: Weaviate client not initialized. Call connect() first.\n    at WeaviateConnection.getClient (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:51:13)\n    at WeaviateConnection.healthCheck (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:388:27)\n    at WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:27:18)\n    at connectWeaviate (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:466:57)\n    at Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:31:21:3121"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:315:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:33:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:32:22:3222"}
{"error":"Weaviate client not initialized. Call connect() first.","level":"error","message":"Weaviate health check failed","stack":"Error: Weaviate client not initialized. Call connect() first.\n    at WeaviateConnection.getClient (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:55:13)\n    at WeaviateConnection.healthCheck (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:392:27)\n    at WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:27:36)\n    at connectWeaviate (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:470:57)\n    at Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"warn","message":"⚠️ Weaviate health check failed, but continuing without vector database","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:32:33:3233"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:32:35:3235"}
{"error":"Weaviate client not initialized. Vector database features are not available.","level":"error","message":"Weaviate health check failed","stack":"Error: Weaviate client not initialized. Vector database features are not available.\n    at WeaviateConnection.getClient (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:55:13)\n    at WeaviateConnection.healthCheck (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:392:27)\n    at WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:27:36)\n    at connectWeaviate (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:470:57)\n    at Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"warn","message":"⚠️ Weaviate health check failed, but continuing without vector database","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:32:46:3246"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:32:47:3247"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:47:3247"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:33:07:337"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:33:08:338"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:08:338"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:08:338"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:33:18:3318"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:18:3318"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:34:04:344"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:34:04:344"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:34:04:344"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:37:59:3759"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:37:59:3759"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:37:59:3759"}
{"level":"info","message":"AG-UI WebSocket connection established: 3c4645cc-26e8-4f3a-8167-84265d613442","timestamp":"2025-06-03 23:43:12:4312"}
{"level":"info","message":"AG-UI WebSocket connection established: 0a23fff3-2f56-46eb-b821-f2ec3ab5f5e3","timestamp":"2025-06-03 23:43:12:4312"}
{"level":"info","message":"AG-UI WebSocket connection closed: 3c4645cc-26e8-4f3a-8167-84265d613442","timestamp":"2025-06-03 23:43:37:4337"}
{"level":"info","message":"AG-UI WebSocket connection established: a103c921-d0dc-4c16-b76b-a4cd31a5bd65","timestamp":"2025-06-03 23:43:37:4337"}
{"level":"info","message":"AG-UI WebSocket connection closed: a103c921-d0dc-4c16-b76b-a4cd31a5bd65","timestamp":"2025-06-03 23:43:37:4337"}
{"level":"info","message":"AG-UI WebSocket connection established: 6cb03ca4-b236-419a-995e-f9cff990892a","timestamp":"2025-06-03 23:43:37:4337"}
{"level":"info","message":"AG-UI WebSocket connection established: 90712659-6bed-4058-a54c-b9cdfabd7ac3","timestamp":"2025-06-03 23:43:38:4338"}
{"level":"info","message":"AG-UI WebSocket connection established: d5931b92-5355-4436-abff-8bc391378c83","timestamp":"2025-06-03 23:43:53:4353"}
{"level":"info","message":"AG-UI WebSocket connection closed: d5931b92-5355-4436-abff-8bc391378c83","timestamp":"2025-06-03 23:43:53:4353"}
{"level":"info","message":"AG-UI WebSocket connection established: d94cb67f-1c36-437a-a039-3e95bd959c7f","timestamp":"2025-06-03 23:43:53:4353"}
{"level":"info","message":"AG-UI WebSocket connection established: 13f9e025-c2f0-4358-8420-5de0197eefe0","timestamp":"2025-06-03 23:43:54:4354"}
{"cause":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"port":11434,"syscall":"connect"},"level":"error","message":"Error processing natural language query: fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async post (/home/<USER>/Suplementor/backend/node_modules/ollama/dist/browser.cjs:135:20)\n    at async Ollama.processStreamableRequest (/home/<USER>/Suplementor/backend/node_modules/ollama/dist/browser.cjs:281:22)\n    at async GemmaService.processNaturalLanguageQuery (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:120:24)\n    at async handleNaturalLanguageQuery (/home/<USER>/Suplementor/backend/src/routes/agui.ts:206:22)\n    at async handleAGUIMessage (/home/<USER>/Suplementor/backend/src/routes/agui.ts:84:7)\n    at async WebSocket.<anonymous> (/home/<USER>/Suplementor/backend/src/routes/agui.ts:31:9)","timestamp":"2025-06-03 23:43:55:4355"}
{"level":"info","message":"AG-UI WebSocket connection closed: 13f9e025-c2f0-4358-8420-5de0197eefe0","timestamp":"2025-06-03 23:44:28:4428"}
{"level":"info","message":"AG-UI WebSocket connection established: 876852af-3251-492f-9bb0-ee5d2dd04d58","timestamp":"2025-06-03 23:44:42:4442"}
{"level":"info","message":"AG-UI WebSocket connection closed: 876852af-3251-492f-9bb0-ee5d2dd04d58","timestamp":"2025-06-03 23:44:42:4442"}
{"level":"info","message":"AG-UI WebSocket connection established: 35ddec15-3e91-43e0-816a-9406d5c5b430","timestamp":"2025-06-03 23:44:42:4442"}
{"level":"info","message":"AG-UI WebSocket connection established: dc962a3e-d389-45d8-8c2f-ab78c21c2c75","timestamp":"2025-06-03 23:44:43:4443"}
{"level":"info","message":"AG-UI WebSocket connection closed: 0a23fff3-2f56-46eb-b821-f2ec3ab5f5e3","timestamp":"2025-06-03 23:44:50:4450"}
{"level":"info","message":"AG-UI WebSocket connection established: aa192fbd-cc2e-470f-9306-d6386366b98c","timestamp":"2025-06-03 23:44:50:4450"}
{"level":"info","message":"AG-UI WebSocket connection closed: 90712659-6bed-4058-a54c-b9cdfabd7ac3","timestamp":"2025-06-03 23:44:50:4450"}
{"level":"info","message":"AG-UI WebSocket connection established: de480c46-9690-4904-b770-4added2915cc","timestamp":"2025-06-03 23:44:50:4450"}
{"level":"info","message":"Starting Gemma analysis for supplement: witamina b12","timestamp":"2025-06-03 23:44:56:4456"}
{"cause":{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"port":11434,"syscall":"connect"},"level":"error","message":"Error in Gemma analysis for witamina b12: fetch failed","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async post (/home/<USER>/Suplementor/backend/node_modules/ollama/dist/browser.cjs:135:20)\n    at async Ollama.processStreamableRequest (/home/<USER>/Suplementor/backend/node_modules/ollama/dist/browser.cjs:281:22)\n    at async GemmaService.analyzeSupplementData (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:63:24)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:108:24)","timestamp":"2025-06-03 23:44:56:4456"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*witamina b12*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-03 23:44:57:4457"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"witamina b12"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1.0 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-03 23:44:58:4458"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"witamina b12","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-03 23:44:58:4458"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"witamina b12","timestamp":"2025-06-03 23:44:58:4458"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-03 23:44:58:4458"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 90712659-6bed-4058-a54c-b9cdfabd7ac3-5f52ad13-638a-432d-81ef-7ca8f2fdca70: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-03 23:44:58:4458"}
{"level":"info","message":"AG-UI WebSocket connection closed: dc962a3e-d389-45d8-8c2f-ab78c21c2c75","timestamp":"2025-06-03 23:45:21:4521"}
{"level":"info","message":"AG-UI WebSocket connection established: c7b03a93-e720-4dfd-8e67-60ecfe2936c2","timestamp":"2025-06-03 23:45:23:4523"}
{"level":"info","message":"AG-UI WebSocket connection closed: c7b03a93-e720-4dfd-8e67-60ecfe2936c2","timestamp":"2025-06-03 23:45:23:4523"}
{"level":"info","message":"AG-UI WebSocket connection established: 67451e75-4712-4e5c-beb2-61d4b3524448","timestamp":"2025-06-03 23:45:23:4523"}
{"level":"info","message":"AG-UI WebSocket connection established: 9ac70073-0d7a-40da-8c0f-7fa871821a50","timestamp":"2025-06-03 23:45:24:4524"}
{"level":"info","message":"AG-UI WebSocket connection closed: 9ac70073-0d7a-40da-8c0f-7fa871821a50","timestamp":"2025-06-03 23:45:25:4525"}
{"level":"info","message":"AG-UI WebSocket connection established: bb894ee8-ab17-40ff-b5c2-6ff1ad1d2c8a","timestamp":"2025-06-03 23:46:30:4630"}
{"level":"info","message":"AG-UI WebSocket connection closed: bb894ee8-ab17-40ff-b5c2-6ff1ad1d2c8a","timestamp":"2025-06-03 23:46:30:4630"}
{"level":"info","message":"AG-UI WebSocket connection established: e18918b0-7869-45ac-80b9-de4f303faf23","timestamp":"2025-06-03 23:46:30:4630"}
{"level":"info","message":"AG-UI WebSocket connection established: 025f9c46-0b34-4116-8445-09f092f25a6d","timestamp":"2025-06-03 23:46:31:4631"}
{"level":"info","message":"AG-UI WebSocket connection closed: 025f9c46-0b34-4116-8445-09f092f25a6d","timestamp":"2025-06-03 23:46:46:4646"}
{"level":"info","message":"AG-UI WebSocket connection established: d19388f5-213f-41dc-b5b6-3e2971341053","timestamp":"2025-06-03 23:46:48:4648"}
{"level":"info","message":"AG-UI WebSocket connection closed: d19388f5-213f-41dc-b5b6-3e2971341053","timestamp":"2025-06-03 23:46:48:4648"}
{"level":"info","message":"AG-UI WebSocket connection established: ed517482-3055-49da-bf76-325358e0ce78","timestamp":"2025-06-03 23:46:48:4648"}
{"level":"info","message":"AG-UI WebSocket connection established: 59799074-dbaf-4d32-9ed9-d0aed077f5d5","timestamp":"2025-06-03 23:46:49:4649"}
{"level":"info","message":"AG-UI WebSocket connection closed: 59799074-dbaf-4d32-9ed9-d0aed077f5d5","timestamp":"2025-06-03 23:46:49:4649"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:47:43:4743"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:47:50:4750"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:47:50:4750"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:47:50:4750"}
{"level":"info","message":"AG-UI WebSocket connection established: b6fec486-5ec7-40a6-af58-ebbf8c70bee1","timestamp":"2025-06-03 23:47:51:4751"}
{"level":"info","message":"AG-UI WebSocket connection established: 23dfce89-9368-4696-ba5d-3ea82ea1c1b4","timestamp":"2025-06-03 23:47:51:4751"}
{"level":"info","message":"AG-UI WebSocket connection established: 57e8b341-1d2a-4061-94ca-d9d287636cd6","timestamp":"2025-06-03 23:47:51:4751"}
{"level":"info","message":"AG-UI WebSocket connection established: bb4307cb-5c6c-4728-bf70-fd0ff273995c","timestamp":"2025-06-03 23:47:51:4751"}
{"level":"info","message":"AG-UI WebSocket connection established: 6bbfca52-1a27-42cb-b336-ffe46437bd35","timestamp":"2025-06-03 23:47:51:4751"}
{"level":"info","message":"AG-UI WebSocket connection established: 61be1435-772a-43f8-97bf-e0723273514b","timestamp":"2025-06-03 23:47:51:4751"}
{"level":"info","message":"AG-UI WebSocket connection established: 8343178a-66a3-4f09-92e2-5f1995b58b59","timestamp":"2025-06-03 23:47:51:4751"}
{"level":"info","message":"AG-UI WebSocket connection established: 3ecc75fe-9628-4429-8920-87e0959c8f54","timestamp":"2025-06-03 23:47:51:4751"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:47:56:4756"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:48:03:483"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:03:483"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:03:483"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:48:03:483"}
{"level":"info","message":"AG-UI WebSocket connection established: 8fb5bf82-783d-46ca-b2f1-41ba6b6015d4","timestamp":"2025-06-03 23:48:04:484"}
{"level":"info","message":"AG-UI WebSocket connection established: 2c5fa691-02e2-4ba8-a431-9f5aa43ab0ba","timestamp":"2025-06-03 23:48:04:484"}
{"level":"info","message":"AG-UI WebSocket connection established: 8d91399b-74e2-494f-9465-2d545c68a570","timestamp":"2025-06-03 23:48:04:484"}
{"level":"info","message":"AG-UI WebSocket connection established: 1ea69f8d-ea76-41ec-8668-257264b2190b","timestamp":"2025-06-03 23:48:04:484"}
{"level":"info","message":"AG-UI WebSocket connection established: 64d385d0-1797-4201-bffa-48f796d7fa8a","timestamp":"2025-06-03 23:48:04:484"}
{"level":"info","message":"AG-UI WebSocket connection established: 52cdde33-02f5-4a17-8b53-28aa941ae65a","timestamp":"2025-06-03 23:48:04:484"}
{"level":"info","message":"AG-UI WebSocket connection established: 03faff82-6c00-417f-8ab5-12d529a47995","timestamp":"2025-06-03 23:48:04:484"}
{"level":"info","message":"AG-UI WebSocket connection established: 1f9d70af-516b-46e1-83d1-248754c5d24d","timestamp":"2025-06-03 23:48:04:484"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:48:09:489"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:48:16:4816"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:16:4816"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:48:16:4816"}
{"level":"info","message":"AG-UI WebSocket connection established: b0cdedd0-244a-4040-b3ff-abe47a150865","timestamp":"2025-06-03 23:48:17:4817"}
{"level":"info","message":"AG-UI WebSocket connection established: 8550741f-9523-4f98-a1f2-46bbcebf1c34","timestamp":"2025-06-03 23:48:17:4817"}
{"level":"info","message":"AG-UI WebSocket connection established: 709da57c-564f-4e03-9dab-c77bad4ee748","timestamp":"2025-06-03 23:48:17:4817"}
{"level":"info","message":"AG-UI WebSocket connection established: e599c26d-4cce-48c3-86a1-e1d93e46ae3e","timestamp":"2025-06-03 23:48:17:4817"}
{"level":"info","message":"AG-UI WebSocket connection established: 2bfdef49-9966-432d-9c51-ae014c374f42","timestamp":"2025-06-03 23:48:17:4817"}
{"level":"info","message":"AG-UI WebSocket connection established: 887d073c-6c87-4380-a37b-45ccd09e89ad","timestamp":"2025-06-03 23:48:17:4817"}
{"level":"info","message":"AG-UI WebSocket connection established: 15379b45-b4ce-42a1-b129-9baed6bb8aa2","timestamp":"2025-06-03 23:48:17:4817"}
{"level":"info","message":"AG-UI WebSocket connection established: 5c00cae1-dfb2-4976-93a5-6b41aff88857","timestamp":"2025-06-03 23:48:17:4817"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:48:20:4820"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:48:27:4827"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:27:4827"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"AG-UI WebSocket connection established: 29351643-11fa-450f-87a4-571297f53527","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"AG-UI WebSocket connection established: cd47ea89-6ed9-4b73-a07a-89bf784aa21a","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"AG-UI WebSocket connection established: 38e6eb4c-a628-4dc4-9e78-c245427f0ed7","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"AG-UI WebSocket connection established: 46b8b4d1-98f5-4af2-bff9-920d2ff4323c","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"AG-UI WebSocket connection established: a5d08960-6663-449a-ad9d-204a4d4c0988","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"AG-UI WebSocket connection established: be3933cd-32f2-4ef8-a8f0-f8a53d63a26b","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"AG-UI WebSocket connection established: c29c91c0-162d-427d-8e3f-4bd2214b430c","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"AG-UI WebSocket connection established: 057d2bbb-1b8c-46ab-bb34-de71689aaaa7","timestamp":"2025-06-03 23:48:27:4827"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:48:29:4829"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:48:36:4836"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:36:4836"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"AG-UI WebSocket connection established: a4e73a64-aedd-4199-ab26-7f93f76a16a3","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"AG-UI WebSocket connection established: 6cb8e6d1-fbfd-408f-95e0-d47362223a92","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"AG-UI WebSocket connection established: bebee71d-6ab7-44cb-baf0-b4dc21198e3a","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"AG-UI WebSocket connection established: 289175c1-7e65-489c-bb74-43ce5a654cca","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"AG-UI WebSocket connection established: 67d63e5d-eb0e-463f-881a-a1fe222db6a1","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"AG-UI WebSocket connection established: 26fa30c7-0f26-48df-889b-d74548fa10ed","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"AG-UI WebSocket connection established: bbf581b2-aa4f-47a5-95b5-a839b2798936","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"AG-UI WebSocket connection established: 73ce1b0e-5078-4ad5-b4b9-e7e1ded0b735","timestamp":"2025-06-03 23:48:36:4836"}
{"level":"info","message":"AG-UI WebSocket connection established: 7de5e91b-0fe4-4dae-b5dc-9c4d042f839f","timestamp":"2025-06-03 23:48:46:4846"}
{"level":"info","message":"AG-UI WebSocket connection closed: 7de5e91b-0fe4-4dae-b5dc-9c4d042f839f","timestamp":"2025-06-03 23:48:46:4846"}
{"level":"info","message":"AG-UI WebSocket connection established: cc981d34-83b5-48a4-8630-2c15e17422a0","timestamp":"2025-06-03 23:48:46:4846"}
{"level":"info","message":"AG-UI WebSocket connection established: c554337d-a274-4d6d-a8f6-7d9f052d5606","timestamp":"2025-06-03 23:48:47:4847"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:48:58:4858"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:49:05:495"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:49:05:495"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:49:05:495"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: e04172b7-01a8-4ced-bbb0-9a861625e1b8","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: d1d3c255-6106-471b-a7a3-89cbb704541d","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: a5db56b6-7cce-4893-93dc-47f2683d1f01","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: db6261cd-f8ae-4616-b464-fda44ab1825e","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: 0f606164-3472-476d-bd55-03bb611c9c5b","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: d16b4b98-2567-420c-ba4b-1c1e312f9876","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: 3d23c3e1-fa08-4b6e-a0ba-3f3d8193261f","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: cad23271-58de-4340-a12c-6ff809fbc0e4","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: c09b3fee-24b1-4f0a-94c4-17a288bdc391","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection established: d2d84990-298d-48ca-ba54-a68151f1fa88","timestamp":"2025-06-03 23:49:05:495"}
{"level":"info","message":"AG-UI WebSocket connection closed: c09b3fee-24b1-4f0a-94c4-17a288bdc391","timestamp":"2025-06-03 23:49:21:4921"}
{"level":"info","message":"AG-UI WebSocket connection established: 97330098-9fc6-4cd3-9072-33bc9e2072d1","timestamp":"2025-06-03 23:49:49:4949"}
{"level":"info","message":"AG-UI WebSocket connection closed: 97330098-9fc6-4cd3-9072-33bc9e2072d1","timestamp":"2025-06-03 23:49:49:4949"}
{"level":"info","message":"AG-UI WebSocket connection established: bf2cc6db-94ae-4398-af3d-1113cbe9aef8","timestamp":"2025-06-03 23:49:49:4949"}
{"level":"info","message":"AG-UI WebSocket connection established: cac892e2-d684-4bcc-9c08-e646004e0567","timestamp":"2025-06-03 23:49:50:4950"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:51:45:5145"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:51:45:5145"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:51:45:5145"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:51:45:5145"}
{"contentLength":"353","duration":12,"ip":"::1","level":"info","message":"API: GET /api/ai/models - 200 (12ms)","method":"GET","statusCode":200,"timestamp":"2025-06-03 23:52:03:523","url":"/api/ai/models","userAgent":"curl/8.5.0"}
{"duration":33436,"level":"info","message":"AI: Chat with gemma-3-4b-it-qat (33436ms)","model":"gemma-3-4b-it-qat","operation":"Chat","timestamp":"2025-06-03 23:52:43:5243"}
{"contentLength":"5301","duration":33443,"ip":"::1","level":"info","message":"API: POST /api/ai/chat - 200 (33443ms)","method":"POST","statusCode":200,"timestamp":"2025-06-03 23:52:43:5243","url":"/api/ai/chat","userAgent":"curl/8.5.0"}
{"level":"info","message":"AG-UI WebSocket connection closed: d1d3c255-6106-471b-a7a3-89cbb704541d","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: 3d23c3e1-fa08-4b6e-a0ba-3f3d8193261f","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: d16b4b98-2567-420c-ba4b-1c1e312f9876","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: 0f606164-3472-476d-bd55-03bb611c9c5b","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: db6261cd-f8ae-4616-b464-fda44ab1825e","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: a5db56b6-7cce-4893-93dc-47f2683d1f01","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: cac892e2-d684-4bcc-9c08-e646004e0567","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: bf2cc6db-94ae-4398-af3d-1113cbe9aef8","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: d2d84990-298d-48ca-ba54-a68151f1fa88","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: cad23271-58de-4340-a12c-6ff809fbc0e4","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection established: 5d763019-78fd-45b1-bf75-7d19e08533e5","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection closed: 5d763019-78fd-45b1-bf75-7d19e08533e5","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection established: 1f24dd11-aa86-409c-a001-677d55cb9156","timestamp":"2025-06-03 23:53:18:5318"}
{"level":"info","message":"AG-UI WebSocket connection established: f021502f-bafd-4450-819d-decc8ffad269","timestamp":"2025-06-03 23:53:19:5319"}
{"level":"info","message":"AG-UI WebSocket connection established: 4f525164-da45-42f7-b800-0eb18d371e8f","timestamp":"2025-06-03 23:53:19:5319"}
{"level":"info","message":"AG-UI WebSocket connection closed: 4f525164-da45-42f7-b800-0eb18d371e8f","timestamp":"2025-06-03 23:53:19:5319"}
{"level":"info","message":"AG-UI WebSocket connection established: c98f290d-8b6b-4ed0-a22a-d52f3eeae0c2","timestamp":"2025-06-03 23:53:19:5319"}
{"level":"info","message":"AG-UI WebSocket connection established: 3d1569cc-1c62-4452-8be5-5c874fe2d538","timestamp":"2025-06-03 23:53:20:5320"}
{"level":"info","message":"AG-UI WebSocket connection closed: 1f24dd11-aa86-409c-a001-677d55cb9156","timestamp":"2025-06-03 23:54:06:546"}
{"level":"info","message":"AG-UI WebSocket connection closed: 3d1569cc-1c62-4452-8be5-5c874fe2d538","timestamp":"2025-06-03 23:54:06:546"}
{"level":"info","message":"AG-UI WebSocket connection closed: c98f290d-8b6b-4ed0-a22a-d52f3eeae0c2","timestamp":"2025-06-03 23:54:06:546"}
{"level":"info","message":"AG-UI WebSocket connection closed: f021502f-bafd-4450-819d-decc8ffad269","timestamp":"2025-06-03 23:54:06:546"}
{"level":"info","message":"AG-UI WebSocket connection established: fc16998c-41f2-4e1e-9a0e-9faa0d20e763","timestamp":"2025-06-03 23:54:06:546"}
{"level":"info","message":"AG-UI WebSocket connection closed: fc16998c-41f2-4e1e-9a0e-9faa0d20e763","timestamp":"2025-06-03 23:54:06:546"}
{"level":"info","message":"AG-UI WebSocket connection established: 703d594b-a848-49eb-b58a-d6b1c744a642","timestamp":"2025-06-03 23:54:06:546"}
{"level":"info","message":"AG-UI WebSocket connection established: 1e9591c3-a1d4-4734-bf69-15300362ae03","timestamp":"2025-06-03 23:54:07:547"}
{"level":"info","message":"AG-UI WebSocket connection closed: 703d594b-a848-49eb-b58a-d6b1c744a642","timestamp":"2025-06-03 23:54:45:5445"}
{"level":"info","message":"AG-UI WebSocket connection closed: 1e9591c3-a1d4-4734-bf69-15300362ae03","timestamp":"2025-06-03 23:54:45:5445"}
{"level":"info","message":"AG-UI WebSocket connection established: 6bce1b2f-4fe6-4d03-a9cd-a3383c008749","timestamp":"2025-06-03 23:54:45:5445"}
{"level":"info","message":"AG-UI WebSocket connection closed: 6bce1b2f-4fe6-4d03-a9cd-a3383c008749","timestamp":"2025-06-03 23:54:45:5445"}
{"level":"info","message":"AG-UI WebSocket connection established: a0ec0546-3304-4e80-906e-062ad3873b71","timestamp":"2025-06-03 23:54:45:5445"}
{"level":"info","message":"AG-UI WebSocket connection established: 506b0e47-9991-48da-89e0-3eb79b2c5b8b","timestamp":"2025-06-03 23:54:46:5446"}
