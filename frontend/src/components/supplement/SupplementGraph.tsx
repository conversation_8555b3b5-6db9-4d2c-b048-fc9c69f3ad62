import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import Card from '@/components/atoms/Card';
import { <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '../ui/card';
import Button from '@/components/atoms/Button';
import Badge from '@/components/atoms/Badge';
import { ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';

interface GraphNode extends d3.SimulationNodeDatum {
  id: string;
  label: string;
  type: 'supplement' | 'health_domain' | 'property' | 'mechanism';
  properties: any;
}

interface GraphEdge {
  id: string;
  source: string | GraphNode;
  target: string | GraphNode;
  type: string;
  strength: number;
  properties: any;
}

interface SupplementGraphProps {
  data: {
    nodes: GraphNode[];
    edges: GraphEdge[];
    healthDomains: any[];
    properties: any[];
  };
  onNodeClick?: (node: GraphNode) => void;
  onEdgeClick?: (edge: GraphEdge) => void;
}

export const SupplementGraph: React.FC<SupplementGraphProps> = ({
  data,
  onNodeClick,
  onEdgeClick
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [zoom, setZoom] = useState<d3.ZoomBehavior<SVGSVGElement, unknown> | null>(null);
  const [simulation, setSimulation] = useState<d3.Simulation<GraphNode, GraphEdge> | null>(null);

  // Health domain colors
  const healthDomainColors: { [key: string]: string } = {
    'neurological': '#8B5CF6', // Purple
    'core_property': '#10B981', // Green
    'supplement': '#3B82F6', // Blue
    'mechanism': '#F59E0B', // Amber
    'property': '#EF4444' // Red
  };

  // Polish health properties mapping
  const polishHealthProperties: { [key: string]: string } = {
    'Energia': 'Energy',
    'Nastrój': 'Mood',
    'Relaks': 'Relaxation',
    'Sen': 'Sleep',
    'Pamięć': 'Memory',
    'Skupienie': 'Focus',
    'Siła': 'Strength',
    'Dieta': 'Diet',
    'Detoks': 'Detox',
    'Witalność': 'Vitality',
    'Odporność': 'Immunity',
    'Libido': 'Libido',
    'OUN': 'Central Nervous System',
    'Neuroregulation': 'Neuroregulation',
    'Neurotransmitters': 'Neurotransmitters'
  };

  useEffect(() => {
    if (!data || !svgRef.current) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const width = 800;
    const height = 600;

    // Create zoom behavior
    const zoomBehavior = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        container.attr('transform', event.transform);
      });

    svg.call(zoomBehavior);
    setZoom(zoomBehavior);

    // Create container for zoomable content
    const container = svg.append('g');

    // Create force simulation
    const forceSimulation = d3.forceSimulation<GraphNode>(data.nodes)
      .force('link', d3.forceLink<GraphNode, GraphEdge>(data.edges)
        .id(d => d.id)
        .distance(d => 100 - (d.strength * 50))
        .strength(d => d.strength * 0.8)
      )
      .force('charge', d3.forceManyBody<GraphNode>()
        .strength(d => (d as GraphNode).type === 'supplement' ? -300 : -150)
      )
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide<GraphNode>()
        .radius(d => getNodeRadius(d as GraphNode) + 5)
      );

    setSimulation(forceSimulation);

    // Create arrow markers for directed edges
    const defs = container.append('defs');
    
    Object.entries(healthDomainColors).forEach(([type, color]) => {
      defs.append('marker')
        .attr('id', `arrow-${type}`)
        .attr('viewBox', '0 -5 10 10')
        .attr('refX', 15)
        .attr('refY', 0)
        .attr('markerWidth', 6)
        .attr('markerHeight', 6)
        .attr('orient', 'auto')
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5')
        .attr('fill', color);
    });

    // Create links
    const links = container.append('g')
      .attr('class', 'links')
      .selectAll('line')
      .data(data.edges)
      .enter()
      .append('line')
      .attr('stroke', d => getEdgeColor(d))
      .attr('stroke-width', d => Math.max(1, d.strength * 4))
      .attr('stroke-opacity', 0.6)
      .attr('marker-end', d => `url(#arrow-${getNodeType(d.target)})`)
      .style('cursor', 'pointer')
      .on('click', (event, d) => {
        event.stopPropagation();
        onEdgeClick?.(d);
      });

    // Create nodes
    const nodes = container.append('g')
      .attr('class', 'nodes')
      .selectAll('g')
      .data(data.nodes)
      .enter()
      .append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer')
      .call(d3.drag<SVGGElement, GraphNode>()
        .on('start', dragStarted)
        .on('drag', dragged)
        .on('end', dragEnded)
      );

    // Add circles to nodes
    nodes.append('circle')
      .attr('r', getNodeRadius)
      .attr('fill', d => healthDomainColors[d.type] || '#6B7280')
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .on('click', (event, d) => {
        event.stopPropagation();
        setSelectedNode(d);
        onNodeClick?.(d);
      });

    // Add labels to nodes
    nodes.append('text')
      .text(d => getNodeLabel(d))
      .attr('x', 0)
      .attr('y', d => getNodeRadius(d) + 15)
      .attr('text-anchor', 'middle')
      .attr('font-size', '12px')
      .attr('font-weight', d => d.type === 'supplement' ? 'bold' : 'normal')
      .attr('fill', '#374151');

    // Add tooltips
    nodes.append('title')
      .text(d => getTooltipText(d));

    // Update positions on simulation tick
    forceSimulation.on('tick', () => {
      links
        .attr('x1', d => (d.source as GraphNode).x!)
        .attr('y1', d => (d.source as GraphNode).y!)
        .attr('x2', d => (d.target as GraphNode).x!)
        .attr('y2', d => (d.target as GraphNode).y!);

      nodes
        .attr('transform', d => `translate(${d.x},${d.y})`);
    });

    // Drag functions
    function dragStarted(event: any, d: GraphNode) {
      if (!event.active) forceSimulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    }

    function dragged(event: any, d: GraphNode) {
      d.fx = event.x;
      d.fy = event.y;
    }

    function dragEnded(event: any, d: GraphNode) {
      if (!event.active) forceSimulation.alphaTarget(0);
      d.fx = null;
      d.fy = null;
    }

    return () => {
      forceSimulation.stop();
    };
  }, [data]);

  const getNodeRadius = (node: GraphNode): number => {
    switch (node.type) {
      case 'supplement': return 20;
      case 'health_domain': return 15;
      case 'property': return 12;
      case 'mechanism': return 10;
      default: return 8;
    }
  };

  const getNodeLabel = (node: GraphNode): string => {
    const label = node.label || node.properties?.name || 'Unknown';
    return polishHealthProperties[label] || label;
  };

  const getNodeType = (node: string | GraphNode): string => {
    if (typeof node === 'string') return 'property';
    return node.type;
  };

  const getEdgeColor = (edge: GraphEdge): string => {
    const targetType = getNodeType(edge.target);
    return healthDomainColors[targetType] || '#6B7280';
  };

  const getTooltipText = (node: GraphNode): string => {
    const lines = [
      `Type: ${node.type}`,
      `Label: ${getNodeLabel(node)}`
    ];
    
    if (node.properties?.confidence) {
      lines.push(`Confidence: ${(node.properties.confidence * 100).toFixed(1)}%`);
    }
    
    if (node.properties?.description) {
      lines.push(`Description: ${node.properties.description}`);
    }
    
    return lines.join('\n');
  };

  const handleZoomIn = () => {
    if (zoom && svgRef.current) {
      d3.select(svgRef.current).transition().call(
        zoom.scaleBy, 1.5
      );
    }
  };

  const handleZoomOut = () => {
    if (zoom && svgRef.current) {
      d3.select(svgRef.current).transition().call(
        zoom.scaleBy, 1 / 1.5
      );
    }
  };

  const handleReset = () => {
    if (zoom && svgRef.current) {
      d3.select(svgRef.current).transition().call(
        zoom.transform,
        d3.zoomIdentity
      );
    }
    if (simulation) {
      simulation.alpha(1).restart();
    }
  };

  return (
    <div className="space-y-4">
      {/* Graph Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Knowledge Graph</CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleZoomIn}>
                <ZoomIn className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleZoomOut}>
                <ZoomOut className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleReset}>
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Legend */}
          <div className="flex flex-wrap gap-2 mb-4">
            {Object.entries(healthDomainColors).map(([type, color]) => (
              <Badge key={type} variant="secondary" className="flex items-center gap-1">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: color }}
                />
                {type.replace('_', ' ').toUpperCase()}
              </Badge>
            ))}
          </div>

          {/* Graph SVG */}
          <div className="border rounded-lg overflow-hidden">
            <svg
              ref={svgRef}
              width="800"
              height="600"
              className="bg-gray-50 dark:bg-gray-900"
            />
          </div>

          {/* Selected Node Info */}
          {selectedNode && (
            <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h4 className="font-semibold mb-2">Selected: {getNodeLabel(selectedNode)}</h4>
              <div className="space-y-1 text-sm">
                <p><strong>Type:</strong> {selectedNode.type}</p>
                {selectedNode.properties?.confidence && (
                  <p><strong>Confidence:</strong> {(selectedNode.properties.confidence * 100).toFixed(1)}%</p>
                )}
                {selectedNode.properties?.description && (
                  <p><strong>Description:</strong> {selectedNode.properties.description}</p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
